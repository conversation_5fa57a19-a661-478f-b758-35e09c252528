# GIP 桩基属性批量修改功能

基于Qt框架开发的桩基属性管理组件，支持桩基数据的批量编辑、过滤、导入导出等功能。

## 功能特性

- ✅ 桩基属性数据的表格展示和编辑
- ✅ 支持新增、删除桩基属性
- ✅ 数据过滤和搜索功能
- ✅ 数据导入导出（JSON格式）
- ✅ 数据验证和错误提示
- ✅ 可扩展的插件化架构
- ✅ 国际化支持

## 系统要求

- Qt 6.0 或更高版本
- C++17 编译器
- CMake 3.16 或更高版本

## 项目结构

```
GIPPileProperty/
├── include/                    # 头文件目录
│   ├── GIPPilePropertyDataItem.h
│   ├── GIPPilePropertyDataMgr.h
│   ├── GIPPilePropertyTableModel.h
│   ├── GIPPilePropertyTableView.h
│   ├── GIPPilePropertyDelegate.h
│   ├── GIPPilePropertyWidget.h
│   ├── GIPPilePropertyController.h
│   └── GIPPilePropertyValidator.h
├── src/                        # 源文件目录
│   ├── GIPPilePropertyDataItem.cpp
│   ├── GIPPilePropertyDataMgr.cpp
│   ├── GIPPilePropertyTableModel.cpp
│   ├── GIPPilePropertyTableView.cpp
│   ├── GIPPilePropertyDelegate.cpp
│   ├── GIPPilePropertyWidget.cpp
│   ├── GIPPilePropertyController.cpp
│   └── GIPPilePropertyValidator.cpp
├── resources/                  # 资源文件目录
│   ├── icons/                  # 图标文件
│   ├── styles/                 # 样式文件
│   └── GIPPileProperty.qrc     # Qt资源文件
├── ui/                         # UI文件目录（可选）
├── docs/                       # 文档目录
├── CMakeLists.txt              # CMake构建文件
├── GIP_PileProperty_Design_Document.md  # 设计文档
├── GIP_PileProperty_Usage_Example.cpp   # 使用示例
└── README.md                   # 项目说明
```

## 编译构建

### 使用CMake构建

```bash
# 创建构建目录
mkdir build
cd build

# 配置项目
cmake ..

# 编译项目
cmake --build .

# 安装（可选）
cmake --install .
```

### 使用Qt Creator

1. 打开Qt Creator
2. 选择"打开项目"
3. 选择CMakeLists.txt文件
4. 配置构建套件
5. 点击"构建"按钮

## 使用方法

### 基本使用

```cpp
#include <QApplication>
#include "GIPPilePropertyWidget.h"
#include "GIPPilePropertyDataMgr.h"
#include "GIPPilePropertyController.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 创建数据管理器
    auto* dataManager = new GIPPilePropertyDataMgr();
    
    // 创建主界面组件
    auto* widget = new GIPPilePropertyWidget();
    widget->setDataSource(dataManager);
    
    // 创建控制器
    auto* controller = new GIPPilePropertyController();
    controller->setWidget(widget);
    controller->setDataManager(dataManager);
    controller->initialize();
    
    // 显示界面
    widget->show();

    return app.exec();
}
```

### 添加数据

```cpp
// 创建桩基数据项
auto* item = new GIPPilePropertyDataItem();
item->setPartName("左幅1-1桩基");
item->setPosition("左幅");
item->setPierNumber(1);
item->setPileNumber(1);
item->setDrillingMethod("旋挖钻机钻孔");
item->setWorkPosition("陆上");
item->setPileDiameter(1.3);
item->setPileLength(20.0);
item->setTopElevation(0.0);
item->setBottomElevation(-20.0);

// 添加到数据管理器
dataManager->addItem(item);
```

### 数据过滤

```cpp
// 设置过滤关键词
widget->setFilterKeyword("左幅");

// 或者通过模型设置
tableModel->setFilterKeyword("旋挖");
```

### 数据持久化

```cpp
// 保存数据到文件
dataManager->saveToFile("pile_data.json");

// 从文件加载数据
dataManager->loadFromFile("pile_data.json");
```

## API文档

详细的API文档请参考 [GIP_PileProperty_Design_Document.md](GIP_PileProperty_Design_Document.md)

## 扩展开发

### 自定义验证规则

```cpp
class CustomValidator : public GIPPilePropertyValidator
{
public:
    static bool validateCustomRule(const QVariant& value)
    {
        // 实现自定义验证逻辑
        return true;
    }
};
```

### 自定义委托

```cpp
class CustomDelegate : public GIPPilePropertyDelegate
{
    // 重写绘制和编辑方法
};
```

## 许可证

本项目采用 MIT 许可证，详情请参考 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 联系方式

如有问题或建议，请联系开发团队。
