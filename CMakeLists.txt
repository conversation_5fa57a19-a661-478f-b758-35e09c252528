cmake_minimum_required(VERSION 3.16)

project(GIPPileProperty VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt6组件
find_package(Qt6 REQUIRED COMPONENTS Core Widgets)

# 启用Qt的MOC、UIC、RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# 设置包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# 头文件列表
set(HEADERS
    include/GIPPilePropertyDataItem.h
    include/GIPPilePropertyDataMgr.h
    include/GIPPilePropertyTableModel.h
    include/GIPPilePropertyTableView.h
    include/GIPPilePropertyDelegate.h
    include/GIPPilePropertyWidget.h
    include/GIPPilePropertyController.h
    include/GIPPilePropertyValidator.h
)

# 源文件列表
set(SOURCES
    src/GIPPilePropertyDataItem.cpp
    src/GIPPilePropertyDataMgr.cpp
    src/GIPPilePropertyTableModel.cpp
    src/GIPPilePropertyTableView.cpp
    src/GIPPilePropertyDelegate.cpp
    src/GIPPilePropertyWidget.cpp
    src/GIPPilePropertyController.cpp
    src/GIPPilePropertyValidator.cpp
)

# UI文件列表（如果使用Qt Designer）
set(UI_FILES
    ui/GIPPilePropertyWidget.ui
)

# 资源文件
set(RESOURCES
    resources/GIPPileProperty.qrc
)

# 创建静态库
add_library(GIPPilePropertyLib STATIC
    ${HEADERS}
    ${SOURCES}
    ${UI_FILES}
    ${RESOURCES}
)

# 链接Qt库
target_link_libraries(GIPPilePropertyLib
    Qt6::Core
    Qt6::Widgets
)

# 设置库的包含目录
target_include_directories(GIPPilePropertyLib PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# 创建示例可执行文件
add_executable(GIPPilePropertyExample
    GIP_PileProperty_Usage_Example.cpp
)

# 链接库
target_link_libraries(GIPPilePropertyExample
    GIPPilePropertyLib
    Qt6::Core
    Qt6::Widgets
)

# 设置输出目录
set_target_properties(GIPPilePropertyLib PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)

set_target_properties(GIPPilePropertyExample PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 安装规则
install(TARGETS GIPPilePropertyLib
    ARCHIVE DESTINATION lib
)

install(FILES ${HEADERS}
    DESTINATION include/GIPPileProperty
)

install(TARGETS GIPPilePropertyExample
    RUNTIME DESTINATION bin
)

# 如果是Debug模式，启用更多的编译警告
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    if(MSVC)
        target_compile_options(GIPPilePropertyLib PRIVATE /W4)
    else()
        target_compile_options(GIPPilePropertyLib PRIVATE -Wall -Wextra -Wpedantic)
    endif()
endif()

# 设置版本信息
set_target_properties(GIPPilePropertyLib PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

# 生成配置文件
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/GIPPilePropertyConfig.h.in
    ${CMAKE_CURRENT_BINARY_DIR}/GIPPilePropertyConfig.h
)

# 添加配置文件到包含目录
target_include_directories(GIPPilePropertyLib PUBLIC
    ${CMAKE_CURRENT_BINARY_DIR}
)

# 打包配置
set(CPACK_PACKAGE_NAME "GIPPileProperty")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "GIP桩基属性管理组件")
set(CPACK_PACKAGE_VENDOR "GIP Engineering")

include(CPack)
