# GIP 桩基属性批量修改功能设计文档

## 1. 功能需求

基于界面原型分析，需要实现一个桩基属性批量修改管理功能，该功能包含：
- 桩基属性数据的表格展示和编辑
- 支持新增属性、删除属性操作
- 支持重置筛选功能
- 表格包含多个属性列：部位名称、位置、墩台号、桩号、成孔方式、作业位置、桩径、桩长、桩顶标高、桩底标高等

## 2. 功能模块划分

### 2.1 数据层模块
- **GIPPilePropertyDataItem**: 桩基属性数据项
- **GIPPilePropertyDataMgr**: 桩基属性数据管理器

### 2.2 视图层模块
- **GIPPilePropertyTableModel**: 表格数据模型
- **GIPPilePropertyTableView**: 表格视图
- **GIPPilePropertyDelegate**: 表格委托（处理编辑和操作）
- **GIPPilePropertyWidget**: 主界面组件

### 2.3 控制层模块
- **GIPPilePropertyController**: 业务逻辑控制器

## 3. 类结构设计

### 3.1 数据存储对象：GIPPilePropertyDataItem

```cpp
class GIPPilePropertyDataItem
{
public:
    GIPPilePropertyDataItem();
    ~GIPPilePropertyDataItem();
    
    // 属性访问器
    QString getPartName() const;           // 部位名称
    void setPartName(const QString& name);
    
    QString getPosition() const;           // 位置
    void setPosition(const QString& pos);
    
    int getPierNumber() const;             // 墩台号
    void setPierNumber(int number);
    
    int getPileNumber() const;             // 桩号
    void setPileNumber(int number);
    
    QString getDrillingMethod() const;     // 成孔方式
    void setDrillingMethod(const QString& method);
    
    QString getWorkPosition() const;       // 作业位置
    void setWorkPosition(const QString& pos);
    
    double getPileDiameter() const;        // 桩径(m)
    void setPileDiameter(double diameter);
    
    double getPileLength() const;          // 桩长(m)
    void setPileLength(double length);
    
    double getTopElevation() const;        // 桩顶标高(m)
    void setTopElevation(double elevation);
    
    double getBottomElevation() const;     // 桩底标高(m)
    void setBottomElevation(double elevation);
    
    // 数据验证
    bool isValid() const;
    
    // 序列化支持
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);

private:
    QString m_partName;
    QString m_position;
    int m_pierNumber;
    int m_pileNumber;
    QString m_drillingMethod;
    QString m_workPosition;
    double m_pileDiameter;
    double m_pileLength;
    double m_topElevation;
    double m_bottomElevation;
};
```

### 3.2 数据管理对象：GIPPilePropertyDataMgr

```cpp
class GIPPilePropertyDataMgr : public QObject
{
    Q_OBJECT
    
public:
    explicit GIPPilePropertyDataMgr(QObject* parent = nullptr);
    ~GIPPilePropertyDataMgr();
    
    // 数据操作
    void addItem(GIPPilePropertyDataItem* item);
    void removeItem(int index);
    void removeItem(GIPPilePropertyDataItem* item);
    void clearAll();
    
    // 数据访问
    int getItemCount() const;
    GIPPilePropertyDataItem* getItem(int index) const;
    QList<GIPPilePropertyDataItem*> getAllItems() const;
    
    // 数据查找和过滤
    QList<GIPPilePropertyDataItem*> findItems(const QString& keyword) const;
    
    // 数据持久化
    bool saveToFile(const QString& filePath) const;
    bool loadFromFile(const QString& filePath);
    
signals:
    void itemAdded(int index);
    void itemRemoved(int index);
    void dataChanged();

private:
    QList<GIPPilePropertyDataItem*> m_items;
    void clearItems();
};
```

### 3.3 Model对象：GIPPilePropertyTableModel

```cpp
class GIPPilePropertyTableModel : public QAbstractTableModel
{
    Q_OBJECT
    
public:
    enum ColumnIndex {
        COL_PART_NAME = 0,      // 部位名称
        COL_POSITION,           // 位置
        COL_PIER_NUMBER,        // 墩台号
        COL_PILE_NUMBER,        // 桩号
        COL_DRILLING_METHOD,    // 成孔方式
        COL_WORK_POSITION,      // 作业位置
        COL_PILE_DIAMETER,      // 桩径
        COL_PILE_LENGTH,        // 桩长
        COL_TOP_ELEVATION,      // 桩顶标高
        COL_BOTTOM_ELEVATION,   // 桩底标高
        COL_OPERATION,          // 操作列
        COL_COUNT
    };
    
    explicit GIPPilePropertyTableModel(QObject* parent = nullptr);
    ~GIPPilePropertyTableModel();
    
    // QAbstractTableModel接口实现
    int rowCount(const QModelIndex& parent = QModelIndex()) const override;
    int columnCount(const QModelIndex& parent = QModelIndex()) const override;
    QVariant data(const QModelIndex& index, int role = Qt::DisplayRole) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    bool setData(const QModelIndex& index, const QVariant& value, int role = Qt::EditRole) override;
    Qt::ItemFlags flags(const QModelIndex& index) const override;
    
    // 数据源设置
    void setDataSource(GIPPilePropertyDataMgr* dataSource);
    GIPPilePropertyDataMgr* getDataSource() const;
    
    // 过滤功能
    void setFilterKeyword(const QString& keyword);
    QString getFilterKeyword() const;
    
public slots:
    void onDataSourceChanged();
    void onItemAdded(int index);
    void onItemRemoved(int index);

private:
    GIPPilePropertyDataMgr* m_dataSource;
    QString m_filterKeyword;
    QList<GIPPilePropertyDataItem*> m_filteredItems;
    
    void updateFilteredItems();
    QString getColumnName(int column) const;
};
```

## 4. 关键接口定义

### 4.1 主要信号槽接口
```cpp
// 数据变更通知
signals:
    void dataChanged();
    void itemAdded(int index);
    void itemRemoved(int index);
    void filterChanged(const QString& keyword);

// 用户操作响应
public slots:
    void onAddItem();
    void onRemoveItem(int index);
    void onFilterTextChanged(const QString& text);
    void onResetFilter();
    void onSaveData();
    void onLoadData();
```

### 4.2 数据验证接口
```cpp
class GIPPilePropertyValidator
{
public:
    static bool validatePileDiameter(double diameter);
    static bool validatePileLength(double length);
    static bool validateElevation(double elevation);
    static bool validatePileNumber(int number);
    static QString getValidationMessage(const QString& field, const QVariant& value);
};
```

## 5. 数据流向说明

```
用户操作 → GIPPilePropertyWidget → GIPPilePropertyController
    ↓
GIPPilePropertyDataMgr ← → GIPPilePropertyTableModel
    ↓
GIPPilePropertyTableView ← → GIPPilePropertyDelegate
    ↓
界面显示更新
```

### 5.1 数据流向详细说明
1. **用户输入**: 用户通过界面进行添加、删除、编辑、过滤等操作
2. **控制器处理**: GIPPilePropertyController接收用户操作，进行业务逻辑处理
3. **数据管理**: GIPPilePropertyDataMgr负责数据的增删改查和持久化
4. **模型更新**: GIPPilePropertyTableModel监听数据变化，更新表格模型
5. **视图刷新**: GIPPilePropertyTableView自动刷新显示最新数据

## 6. 界面与逻辑的交互方式

### 6.1 MVC架构模式
- **Model**: GIPPilePropertyTableModel负责数据逻辑
- **View**: GIPPilePropertyTableView + GIPPilePropertyWidget负责界面显示
- **Controller**: GIPPilePropertyController负责业务逻辑协调

### 6.2 事件驱动机制
- 使用Qt的信号槽机制实现松耦合的事件通信
- 数据变更自动触发界面更新
- 用户操作通过信号传递给相应的处理函数

## 7. 性能考虑与优化建议

### 7.1 数据处理优化
- 使用智能指针管理内存，避免内存泄漏
- 大数据量时采用分页加载机制
- 过滤操作使用异步处理，避免界面卡顿

### 7.2 界面渲染优化
- 表格使用延迟加载，只渲染可见区域
- 数据变更时使用增量更新而非全量刷新
- 合理设置表格列宽和行高，优化显示效果

### 7.3 内存管理优化
```cpp
// 使用智能指针管理数据项
using GIPPilePropertyDataItemPtr = std::shared_ptr<GIPPilePropertyDataItem>;
using GIPPilePropertyDataItemList = QList<GIPPilePropertyDataItemPtr>;
```

## 8. View对象：GIPPilePropertyTableView

```cpp
class GIPPilePropertyTableView : public QTableView
{
    Q_OBJECT

public:
    explicit GIPPilePropertyTableView(QWidget* parent = nullptr);
    ~GIPPilePropertyTableView();

    // 设置数据模型
    void setTableModel(GIPPilePropertyTableModel* model);

    // 界面配置
    void setupUI();
    void setupColumns();

    // 选择操作
    QList<int> getSelectedRows() const;
    void selectRow(int row);
    void clearSelection();

protected:
    void contextMenuEvent(QContextMenuEvent* event) override;
    void keyPressEvent(QKeyEvent* event) override;

signals:
    void deleteRequested(int row);
    void editRequested(int row);

private slots:
    void onCustomContextMenuRequested(const QPoint& pos);
    void onDeleteAction();
    void onEditAction();

private:
    GIPPilePropertyTableModel* m_model;
    QMenu* m_contextMenu;
    QAction* m_deleteAction;
    QAction* m_editAction;

    void createContextMenu();
};
```

## 9. Delegate对象：GIPPilePropertyDelegate

```cpp
class GIPPilePropertyDelegate : public QStyledItemDelegate
{
    Q_OBJECT

public:
    explicit GIPPilePropertyDelegate(QObject* parent = nullptr);
    ~GIPPilePropertyDelegate();

    // QStyledItemDelegate接口实现
    QWidget* createEditor(QWidget* parent, const QStyleOptionViewItem& option,
                         const QModelIndex& index) const override;
    void setEditorData(QWidget* editor, const QModelIndex& index) const override;
    void setModelData(QWidget* editor, QAbstractItemModel* model,
                     const QModelIndex& index) const override;
    void updateEditorGeometry(QWidget* editor, const QStyleOptionViewItem& option,
                             const QModelIndex& index) const override;
    void paint(QPainter* painter, const QStyleOptionViewItem& option,
              const QModelIndex& index) const override;
    QSize sizeHint(const QStyleOptionViewItem& option, const QModelIndex& index) const override;

    // 事件处理
    bool editorEvent(QEvent* event, QAbstractItemModel* model,
                    const QStyleOptionViewItem& option, const QModelIndex& index) override;

signals:
    void deleteButtonClicked(int row);

private:
    mutable QMap<QPersistentModelIndex, QRect> m_deleteButtonRects;

    QRect getDeleteButtonRect(const QStyleOptionViewItem& option) const;
    void drawDeleteButton(QPainter* painter, const QStyleOptionViewItem& option,
                         const QModelIndex& index) const;
};
```

## 10. 表格组件：GIPPilePropertyWidget

```cpp
class GIPPilePropertyWidget : public QWidget
{
    Q_OBJECT

public:
    explicit GIPPilePropertyWidget(QWidget* parent = nullptr);
    ~GIPPilePropertyWidget();

    // 数据源设置
    void setDataSource(GIPPilePropertyDataMgr* dataSource);

    // 界面操作
    void refreshData();
    void clearFilter();

signals:
    void dataChanged();

private slots:
    void onAddClicked();
    void onDeleteClicked();
    void onSaveClicked();
    void onLoadClicked();
    void onResetFilterClicked();
    void onFilterTextChanged(const QString& text);
    void onDeleteRequested(int row);

private:
    // UI组件
    QToolBar* m_toolBar;
    QLineEdit* m_filterEdit;
    GIPPilePropertyTableView* m_tableView;
    GIPPilePropertyTableModel* m_tableModel;
    GIPPilePropertyDelegate* m_delegate;

    // 工具栏动作
    QAction* m_addAction;
    QAction* m_deleteAction;
    QAction* m_saveAction;
    QAction* m_loadAction;
    QAction* m_resetFilterAction;

    // 数据管理
    GIPPilePropertyDataMgr* m_dataSource;

    void setupUI();
    void setupToolBar();
    void setupTable();
    void connectSignals();
    void updateActions();
};
```

## 11. 控制器：GIPPilePropertyController

```cpp
class GIPPilePropertyController : public QObject
{
    Q_OBJECT

public:
    explicit GIPPilePropertyController(QObject* parent = nullptr);
    ~GIPPilePropertyController();

    // 初始化
    void initialize();
    void setWidget(GIPPilePropertyWidget* widget);
    void setDataManager(GIPPilePropertyDataMgr* dataManager);

    // 业务操作
    bool addNewItem();
    bool deleteItem(int index);
    bool saveData(const QString& filePath = QString());
    bool loadData(const QString& filePath = QString());

    // 数据验证
    bool validateItemData(const GIPPilePropertyDataItem* item, QString& errorMsg) const;

public slots:
    void onAddRequested();
    void onDeleteRequested(int index);
    void onSaveRequested();
    void onLoadRequested();

signals:
    void operationCompleted(const QString& message);
    void operationFailed(const QString& error);

private:
    GIPPilePropertyWidget* m_widget;
    GIPPilePropertyDataMgr* m_dataManager;
    QString m_currentFilePath;

    QString getDefaultFilePath() const;
    void showMessage(const QString& message);
    void showError(const QString& error);
};
```

## 12. 代码组织与文件结构建议

```
GIPPileProperty/
├── include/
│   ├── GIPPilePropertyDataItem.h
│   ├── GIPPilePropertyDataMgr.h
│   ├── GIPPilePropertyTableModel.h
│   ├── GIPPilePropertyTableView.h
│   ├── GIPPilePropertyDelegate.h
│   ├── GIPPilePropertyWidget.h
│   ├── GIPPilePropertyController.h
│   └── GIPPilePropertyValidator.h
├── src/
│   ├── GIPPilePropertyDataItem.cpp
│   ├── GIPPilePropertyDataMgr.cpp
│   ├── GIPPilePropertyTableModel.cpp
│   ├── GIPPilePropertyTableView.cpp
│   ├── GIPPilePropertyDelegate.cpp
│   ├── GIPPilePropertyWidget.cpp
│   ├── GIPPilePropertyController.cpp
│   └── GIPPilePropertyValidator.cpp
├── ui/
│   └── GIPPilePropertyWidget.ui (可选，如果使用Qt Designer)
├── resources/
│   ├── icons/
│   │   ├── add.png
│   │   ├── delete.png
│   │   ├── save.png
│   │   └── load.png
│   └── GIPPileProperty.qrc
└── CMakeLists.txt 或 GIPPileProperty.pro
```

## 13. 必要的注释与文档说明

### 13.1 类注释规范
```cpp
/**
 * @brief 桩基属性数据项类
 * @details 用于存储单个桩基的所有属性信息，包括几何参数、位置信息等
 * <AUTHOR>
 * @date [创建日期]
 * @version 1.0
 */
class GIPPilePropertyDataItem
{
    // ...
};
```

### 13.2 方法注释规范
```cpp
/**
 * @brief 设置桩径
 * @param diameter 桩径值，单位：米，必须大于0
 * @return 无
 * @note 桩径范围应在0.1m-3.0m之间
 */
void setPileDiameter(double diameter);
```

### 13.3 重要算法说明
- 数据过滤算法：使用QString::contains进行模糊匹配
- 数据验证规则：各字段的取值范围和格式要求
- 内存管理策略：使用RAII原则，确保资源正确释放

## 14. 扩展性考虑

### 14.1 插件化支持
- 预留数据导入导出插件接口
- 支持自定义验证规则插件
- 支持自定义渲染委托插件

### 14.2 国际化支持
- 所有用户界面文本使用tr()函数
- 数值格式支持本地化
- 日期时间格式本地化

### 14.3 配置管理
```cpp
class GIPPilePropertyConfig
{
public:
    static GIPPilePropertyConfig& instance();

    // 配置项访问
    QStringList getDrillingMethods() const;
    QStringList getWorkPositions() const;
    double getMinPileDiameter() const;
    double getMaxPileDiameter() const;

    // 配置保存和加载
    void saveConfig();
    void loadConfig();

private:
    // 配置数据
    QStringList m_drillingMethods;
    QStringList m_workPositions;
    double m_minPileDiameter;
    double m_maxPileDiameter;
};
```

## 15. 测试建议

### 15.1 单元测试
- 对每个数据类进行单元测试
- 测试数据验证逻辑
- 测试序列化和反序列化功能

### 15.2 集成测试
- 测试Model-View交互
- 测试数据持久化功能
- 测试用户操作流程

### 15.3 性能测试
- 大数据量加载性能测试
- 过滤操作响应时间测试
- 内存使用情况监控

---

**注意事项：**
1. 所有类名都使用GIP前缀，保持命名一致性
2. 遵循Qt编程规范和C++最佳实践
3. 注意线程安全，特别是在数据访问时
4. 提供充分的错误处理和用户反馈
5. 考虑后续功能扩展的兼容性

**实现优先级建议：**
1. 首先实现数据层（GIPPilePropertyDataItem、GIPPilePropertyDataMgr）
2. 然后实现模型层（GIPPilePropertyTableModel）
3. 接着实现视图层（GIPPilePropertyTableView、GIPPilePropertyDelegate）
4. 最后实现控制层和主界面（GIPPilePropertyController、GIPPilePropertyWidget）

**开发流程建议：**
1. 创建基础数据结构和接口
2. 实现核心功能的最小可用版本
3. 逐步添加高级功能（过滤、验证、持久化等）
4. 进行充分的测试和优化
5. 完善文档和用户手册
