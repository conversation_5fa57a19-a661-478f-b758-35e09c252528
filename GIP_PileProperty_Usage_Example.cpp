/**
 * @file GIP_PileProperty_Usage_Example.cpp
 * @brief GIP桩基属性管理功能使用示例
 * <AUTHOR> Agent
 * @date 2024
 */

#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QMessageBox>

// 假设的头文件包含
#include "GIPPilePropertyWidget.h"
#include "GIPPilePropertyController.h"
#include "GIPPilePropertyDataMgr.h"
#include "GIPPilePropertyDataItem.h"

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget* parent = nullptr) : QMainWindow(parent)
    {
        setupUI();
        setupData();
        connectSignals();
    }

    ~MainWindow()
    {
        // 智能指针和Qt对象树会自动管理内存
    }

private slots:
    void onOperationCompleted(const QString& message)
    {
        statusBar()->showMessage(message, 3000);
    }

    void onOperationFailed(const QString& error)
    {
        QMessageBox::warning(this, "操作失败", error);
    }

private:
    void setupUI()
    {
        // 创建中央窗口部件
        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);

        // 创建布局
        QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);
        
        // 添加标题
        QLabel* titleLabel = new QLabel("GIP 桩基属性批量修改管理", this);
        titleLabel->setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;");
        titleLabel->setAlignment(Qt::AlignCenter);
        mainLayout->addWidget(titleLabel);

        // 创建桩基属性管理组件
        m_pilePropertyWidget = new GIPPilePropertyWidget(this);
        mainLayout->addWidget(m_pilePropertyWidget);

        // 设置窗口属性
        setWindowTitle("GIP 桩基属性管理系统");
        setMinimumSize(1200, 800);
        resize(1400, 900);
    }

    void setupData()
    {
        // 创建数据管理器
        m_dataManager = new GIPPilePropertyDataMgr(this);
        
        // 创建控制器
        m_controller = new GIPPilePropertyController(this);
        m_controller->setWidget(m_pilePropertyWidget);
        m_controller->setDataManager(m_dataManager);
        m_controller->initialize();

        // 设置数据源
        m_pilePropertyWidget->setDataSource(m_dataManager);

        // 添加一些示例数据
        addSampleData();
    }

    void connectSignals()
    {
        // 连接控制器信号
        connect(m_controller, &GIPPilePropertyController::operationCompleted,
                this, &MainWindow::onOperationCompleted);
        connect(m_controller, &GIPPilePropertyController::operationFailed,
                this, &MainWindow::onOperationFailed);
    }

    void addSampleData()
    {
        // 创建示例数据项
        QList<QStringList> sampleData = {
            {"左幅1-1桩基", "左幅", "1", "1", "旋挖钻机钻孔", "陆上", "1.3", "20", "0", "-20"},
            {"左幅1-2桩基", "左幅", "1", "2", "旋挖钻机钻孔", "陆上", "1.3", "20", "0", "-20"},
            {"左幅1-3桩基", "左幅", "1", "3", "旋挖钻机钻孔", "陆上", "1.3", "20", "0", "-20"},
            {"左幅2-1桩基", "左幅", "2", "1", "旋挖钻机钻孔", "陆上", "1.3", "30", "0", "-30"},
            {"左幅2-2桩基", "左幅", "2", "2", "旋挖钻机钻孔", "陆上", "1.3", "30", "0", "-30"},
            {"左幅2-3桩基", "左幅", "2", "3", "冲击钻机钻孔", "陆上", "1.3", "30", "0", "-30"},
            {"左幅3-1桩基", "左幅", "1", "1", "冲击钻机钻孔", "水下", "1.2", "40", "0", "-40"}
        };

        for (const auto& data : sampleData) {
            auto* item = new GIPPilePropertyDataItem();
            item->setPartName(data[0]);
            item->setPosition(data[1]);
            item->setPierNumber(data[2].toInt());
            item->setPileNumber(data[3].toInt());
            item->setDrillingMethod(data[4]);
            item->setWorkPosition(data[5]);
            item->setPileDiameter(data[6].toDouble());
            item->setPileLength(data[7].toDouble());
            item->setTopElevation(data[8].toDouble());
            item->setBottomElevation(data[9].toDouble());

            m_dataManager->addItem(item);
        }
    }

private:
    GIPPilePropertyWidget* m_pilePropertyWidget;
    GIPPilePropertyController* m_controller;
    GIPPilePropertyDataMgr* m_dataManager;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 设置应用程序信息
    app.setApplicationName("GIP桩基属性管理系统");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("GIP Engineering");

    // 创建并显示主窗口
    MainWindow window;
    window.show();

    return app.exec();
}

#include "GIP_PileProperty_Usage_Example.moc"
